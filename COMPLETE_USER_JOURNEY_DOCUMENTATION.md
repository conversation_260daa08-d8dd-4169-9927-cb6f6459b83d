# 🛍️ Complete User Journey: From Account Creation to Product Display

This comprehensive documentation traces the complete user journey from creating an account to seeing products appear on their shop link in real-time.

## 🎯 Overview

The TikTok Commerce Link Hub enables users to:
1. **Create an account** with their TikTok handle
2. **Get a 7-day free trial** with automatic shop creation
3. **Post TikTok videos** with the `#TRACK` hashtag
4. **See products automatically appear** on their shop link in real-time
5. **Manage subscriptions** for continued access

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Frontend (React)"
        A[User Signup] --> B[Shop Page]
        B --> C[Real-time Updates]
    end
    
    subgraph "Backend Services"
        D[Ingestion API] --> E[Product Service]
        F[AI Workers] --> E
    end
    
    subgraph "External Services"
        G[TikTok] --> H[Apify Scraper]
        I[AWS Cognito] --> D
        J[DynamoDB] --> D
        J --> E
    end
    
    subgraph "AI Processing"
        K[Caption Parser] --> F
        L[Thumbnail Generator] --> F
    end
    
    A --> D
    H --> D
    D --> F
    E --> C
```

## 📋 Complete User Journey Flow

### Phase 1: Account Creation & Shop Setup

#### Step 1: User Signup Process
**Location**: `apps/frontend/src/components/SignupFlow.tsx`

1. **User enters TikTok handle** (e.g., `@nalu-fashion`)
   - Frontend validates handle format
   - Calls `POST /auth/validate-handle` to check if handle exists on TikTok

2. **Handle validation** (`apps/ingestion-api/src/auth/services/auth.service.ts:281`)
   ```typescript
   const handleValidation = await this.validateHandle(handle);
   // Checks: TikTok profile exists, not already registered
   ```

3. **User creates password**
   - Frontend validates password strength (min 8 characters)
   - Calls `POST /auth/password-signup`

#### Step 2: Backend Account Creation
**Location**: `apps/ingestion-api/src/auth/services/auth.service.ts:275`

1. **Cognito user creation**
   ```typescript
   const signUpCommand = new SignUpCommand({
     ClientId: this.clientId,
     Username: handle,
     Password: password
   });
   ```

2. **Database user record creation**
   ```typescript
   const userData: CreateUserInput = {
     cognitoUserId: signUpResult.UserSub!,
     handle,
     subscriptionStatus: SubscriptionStatus.TRIAL,
     trialEndsAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
   };
   ```

3. **Shop creation**
   ```typescript
   await this.shopService.createShop({
     handle,
     user_id: user.userId,
     subscription_status: 'trial',
     isActive: true
   });
   ```

#### Step 3: Frontend Shop Access
**Location**: `apps/frontend/src/pages/Shop.tsx`

1. **User redirected to shop page**: `/shop/nalu-fashion`
2. **7-day trial activated** automatically
3. **Shop page displays**:
   - Profile information
   - Trial countdown timer
   - Empty product grid with instructions
   - "Post TikTok videos with #TRACK to add products"

### Phase 2: Video Processing Pipeline

#### Step 1: Scheduled Video Ingestion
**Location**: `apps/ingestion-api/src/ingestion/handlers/scheduled-ingestion.handler.ts`

**Trigger**: AWS EventBridge (06:00 and 18:00 UTC daily)

1. **Lambda function executes**
   ```typescript
   export const handler = async (event: any, context: any) => {
     const app = await NestFactory.createApplicationContext(IngestionModule);
     const ingestionService = app.get(IngestionService);
     await ingestionService.processAllActiveUsers();
   };
   ```

2. **For each active user** (`apps/ingestion-api/src/ingestion/services/ingestion.service.ts:204`)
   ```typescript
   // Extract videos from TikTok using Apify
   const extractionResult = await this.apifyService.extractVideos(handle, 50);
   
   // Filter for videos with #TRACK
   const trackedVideos = this.apifyService.filterTrackedVideos(extractionResult.videos);
   
   // Filter out already processed videos
   const newVideos = trackedVideos.filter(video => !lastVideoIds.includes(video.id));
   ```

3. **Emit video events** for each new #TRACK video
   ```typescript
   await this.emitVideoPostedEvent(handle, video);
   ```

#### Step 2: AI Workers Processing
**Parallel Processing**: Two AI workers process each video simultaneously

##### Caption Parser Worker
**Location**: `apps/ai-workers/caption-parser/src/services/worker.service.ts`

1. **Receives video event** from SQS queue
2. **Extracts product information** using LLM
   ```typescript
   const parsedData = await this.llmService.parseCaptionWithLLM(videoEvent.caption);
   ```
3. **Creates structured product data**:
   - Title, price, sizes, tags
   - Confidence score
   - Raw caption
4. **Publishes caption parsed event** to SNS

##### Thumbnail Generator Worker
**Location**: `apps/ai-workers/thumbnail-generator/src/services/worker.service.ts`

1. **Receives video event** from SQS queue
2. **Downloads video** and extracts frames
3. **Analyzes frames** using YOLO object detection
   ```typescript
   const frameAnalyses = await this.analyzeFrames(framesDir, videoId);
   ```
4. **Generates multiple thumbnails** with quality scores
5. **Uploads to S3** and publishes thumbnail generated event

#### Step 3: Product Assembly
**Location**: `apps/product-service/src/events/event-processor.service.ts`

1. **Receives both events** (caption + thumbnail)
2. **Merges data** when both are complete
   ```typescript
   assemblyData.is_complete = !!(assemblyData.caption_data && assemblyData.thumbnail_data);
   
   if (assemblyData.is_complete) {
     await this.assembleAndSaveProduct(assemblyData);
   }
   ```

3. **Creates final product**
   ```typescript
   const product: AssembledProduct = {
     seller_handle: assemblyData.seller_handle,
     video_id: assemblyData.video_id,
     title: assemblyData.caption_data.title,
     price: assemblyData.caption_data.price,
     sizes: assemblyData.caption_data.sizes,
     tags: assemblyData.caption_data.tags,
     thumbnails: assemblyData.thumbnail_data.thumbnails,
     primary_thumbnail: assemblyData.thumbnail_data.primary_thumbnail,
     // ... other fields
   };
   ```

4. **Saves to DynamoDB** products table
5. **Triggers real-time notification**

### Phase 3: Real-Time Frontend Updates

#### Step 1: WebSocket Notification
**Location**: `apps/product-service/src/websocket/websocket.gateway.ts`

1. **Product service broadcasts** new product event
   ```typescript
   this.webSocketGateway.broadcastNewProduct(newProductEvent);
   ```

2. **WebSocket emits** to subscribed clients
   ```typescript
   this.server.to(`seller:${sellerHandle}`).emit('new_product', {
     event_type: 'new_product',
     product: event.product,
     timestamp: event.timestamp,
   });
   ```

#### Step 2: Frontend Real-Time Update
**Location**: `apps/frontend/src/hooks/useProductUpdates.ts`

1. **WebSocket client receives** new product event
   ```typescript
   socket.on('new_product', (event: NewProductEvent) => {
     if (event.product.seller_handle === sellerHandle) {
       addNewProduct(sellerHandle, event.product);
       toast.success(`New product added: ${event.product.title}`);
     }
   });
   ```

2. **React Query cache updated**
   ```typescript
   queryClient.setQueryData(productKeys.shopProducts(sellerHandle), (oldData) => ({
     ...oldData,
     data: {
       ...oldData.data,
       products: [newProduct, ...oldData.data.products],
     },
   }));
   ```

3. **Shop page re-renders** with new product
4. **Toast notification** shows success message

### Phase 4: Product Display on Shop

#### Shop Page Product Grid
**Location**: `apps/frontend/src/pages/Shop.tsx:464`

```typescript
{hasProducts ? (
  <div className="grid grid-cols-2 gap-4">
    {products.map((product) => (
      <div key={product.video_id} className="border rounded-lg overflow-hidden">
        <div className="aspect-square bg-muted">
          <img
            src={product.primary_thumbnail.thumbnail_url}
            alt={product.title}
            className="w-full h-full object-cover"
          />
        </div>
        <div className="p-3 space-y-2">
          <h4 className="text-sm font-medium text-foreground line-clamp-2">
            {product.title}
          </h4>
          {product.price && (
            <p className="text-sm font-semibold text-primary">
              UGX {product.price.toLocaleString()}
            </p>
          )}
          {product.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {product.tags.slice(0, 2).map((tag) => (
                <span key={tag} className="text-xs bg-muted text-muted-foreground px-2 py-1 rounded">
                  {tag}
                </span>
              ))}
            </div>
          )}
        </div>
      </div>
    ))}
  </div>
) : (
  <div className="text-center py-8 space-y-2">
    <div className="text-sm text-muted-foreground">No products yet</div>
    {isOwner && (
      <div className="text-xs text-muted-foreground">
        Post TikTok videos with #TRACK to add products
      </div>
    )}
  </div>
)}
```

## ⏱️ Timeline Example

Let's trace a specific example:

### Day 1: Account Creation
- **09:00**: User `@nalu-fashion` signs up
- **09:01**: Shop created at `/shop/nalu-fashion`
- **09:02**: 7-day trial starts (expires Day 8 at 09:01)

### Day 2: First Video
- **14:30**: User posts TikTok video with caption "Check out this amazing dress! Perfect for summer parties 👗 #TRACK #fashion #summer"
- **18:00**: Scheduled ingestion runs
- **18:01**: Video detected with #TRACK hashtag
- **18:02**: AI workers start processing
- **18:03**: Caption parser extracts: "Amazing Summer Dress", price detection, tags
- **18:04**: Thumbnail generator creates 5 thumbnails, selects best one
- **18:05**: Product assembled and saved
- **18:05**: WebSocket broadcasts to shop owner
- **18:05**: Product appears on `/shop/nalu-fashion` instantly

### Day 8: Trial Expiry
- **09:01**: Trial expires
- **09:02**: User visits shop, sees trial expiry modal
- **09:03**: User subscribes for UGX 10,000/month
- **09:04**: Subscription activated, shop access restored

## 🔧 Key Technical Components

### Database Schema
```
Users Table (DynamoDB):
- PK: userId
- handle, subscriptionStatus, trialEndDate, createdAt

Shops Table (DynamoDB):
- PK: handle
- user_id, subscription_status, isActive

Products Table (DynamoDB):
- PK: seller_handle
- SK: video_id
- title, price, sizes, tags, thumbnails, created_at
```

### API Endpoints
```
Authentication:
POST /auth/password-signup
POST /auth/signin
GET /auth/subscription-status
POST /auth/subscribe

Shop Management:
GET /shop/{handle}
GET /shop/{handle}/owner
GET /shop/{handle}/products

Product Service:
GET /api/v1/shop/{handle}/products
WebSocket: ws://localhost:3002/products
```

### Environment Variables
```
Frontend:
VITE_API_BASE_URL=http://localhost:3001
VITE_PRODUCT_SERVICE_URL=http://localhost:3002

Backend:
AWS_REGION=us-east-1
COGNITO_USER_POOL_ID=us-east-1_xxx
APIFY_TOKEN=apify_api_xxx
```

## 🚀 Getting Started

1. **Start all services**:
   ```bash
   # Terminal 1: Ingestion API
   cd apps/ingestion-api && npm run dev

   # Terminal 2: Product Service
   cd apps/product-service && npm run dev

   # Terminal 3: Frontend
   cd apps/frontend && npm run dev
   ```

2. **Create account**: Visit `http://localhost:3000`
3. **Post TikTok video**: Add `#TRACK` to any TikTok video
4. **Wait for processing**: Products appear within 5-10 minutes
5. **See real-time updates**: Products appear instantly on shop page

## 🔍 Detailed Code Walkthrough

### 1. Account Creation Flow

#### Frontend Signup Component
**File**: `apps/frontend/src/components/SignupFlow.tsx`

```typescript
// Step 1: Handle validation
const handleNext = async () => {
  if (currentStep === 1) {
    setHandleValidation({ isValid: false, isValidating: true });
    try {
      await validateHandle.mutateAsync({ handle: formData.tiktokHandle });
      setHandleValidation({ isValid: true, isValidating: false });
      setCurrentStep(2);
    } catch (error) {
      setHandleValidation({
        isValid: false,
        isValidating: false,
        message: error.message
      });
    }
  }

  // Step 2: Account creation
  else if (currentStep === 2) {
    try {
      const response = await passwordSignup.mutateAsync({
        handle: formData.tiktokHandle,
        password: formData.password
      });
      setShopLink(response.data.shopLink);
      setCurrentStep(3); // Success step
    } catch (error) {
      // Error handled by mutation
    }
  }
};
```

#### Backend User Creation
**File**: `apps/ingestion-api/src/auth/services/auth.service.ts`

```typescript
async signup(handle: string, password: string): Promise<SignupResponse> {
  // 1. Validate TikTok handle exists
  const handleValidation = await this.validateHandle(handle);

  // 2. Create Cognito user
  const signUpResult = await this.cognitoClient.send(new SignUpCommand({
    ClientId: this.clientId,
    Username: handle,
    Password: password,
  }));

  // 3. Create database user with trial
  const userData: CreateUserInput = {
    cognitoUserId: signUpResult.UserSub!,
    handle,
    subscriptionStatus: SubscriptionStatus.TRIAL,
    trialEndsAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
  };
  const user = await this.userRepository.createUser(userData);

  // 4. Create shop
  await this.shopService.createShop({
    handle,
    user_id: user.userId,
    subscription_status: 'trial',
    isActive: true,
  });

  return { success: true, shopLink: `/shop/${handle}` };
}
```

### 2. Video Processing Pipeline

#### Scheduled Ingestion Trigger
**File**: `apps/ingestion-api/src/ingestion/handlers/scheduled-ingestion.handler.ts`

```typescript
export const handler = async (event: any, context: any) => {
  const app = await NestFactory.createApplicationContext(IngestionModule);
  const ingestionService = app.get(IngestionService);

  // Process all active users (trial + paid subscribers)
  const result = await ingestionService.processAllActiveUsers();

  return {
    statusCode: 200,
    body: JSON.stringify({
      success: true,
      usersProcessed: result.usersProcessed,
      videosFound: result.videosFound,
      eventsEmitted: result.eventsEmitted
    })
  };
};
```

#### Video Detection & Event Emission
**File**: `apps/ingestion-api/src/ingestion/services/ingestion.service.ts`

```typescript
async processUserVideos(handle: string): Promise<ProcessingResult> {
  // 1. Extract recent videos from TikTok
  const extractionResult = await this.apifyService.extractVideos(handle, 50);

  // 2. Filter for #TRACK hashtag
  const trackedVideos = this.apifyService.filterTrackedVideos(extractionResult.videos);

  // 3. Filter out already processed videos
  const lastState = await this.getIngestionState(handle);
  const newVideos = trackedVideos.filter(video =>
    !lastState?.last_video_ids?.includes(video.id)
  );

  // 4. Emit events for each new video
  for (const video of newVideos) {
    await this.emitVideoPostedEvent(handle, video);
  }

  // 5. Update processing state
  await this.updateIngestionState(handle, 'completed', {
    last_video_ids: trackedVideos.map(v => v.id),
    last_processed_at: new Date().toISOString()
  });
}
```

### 3. AI Workers Processing

#### Caption Parser Worker
**File**: `apps/ai-workers/caption-parser/src/services/worker.service.ts`

```typescript
async processMessage(message: SQSMessage): Promise<ProcessingResult> {
  const videoEvent: VideoPostedEvent = JSON.parse(message.Body);

  // 1. Parse caption with LLM
  const parsedData = await this.llmService.parseCaptionWithLLM(videoEvent.caption);

  // 2. Create structured event
  const captionParsedEvent: CaptionParsedEvent = {
    video_id: videoEvent.video_id,
    seller_handle: videoEvent.seller_handle,
    title: parsedData.title,
    price: parsedData.price,
    sizes: parsedData.sizes,
    tags: parsedData.tags,
    confidence_score: parsedData.confidence_score,
    raw_caption: videoEvent.caption,
    timestamp: new Date().toISOString()
  };

  // 3. Publish to SNS for product service
  await this.publishCaptionParsedEvent(captionParsedEvent);

  // 4. Delete processed message
  await this.deleteMessage(message);
}
```

#### Thumbnail Generator Worker
**File**: `apps/ai-workers/thumbnail-generator/src/services/worker.service.ts`

```typescript
async processMessage(message: SQSMessage): Promise<ProcessingResult> {
  const videoEvent: VideoPostedEvent = JSON.parse(message.Body);

  // 1. Download and process video
  const processingResult = await this.videoService.processVideo(
    videoEvent.video_url,
    videoEvent.video_id
  );

  // 2. Upload thumbnails to S3
  const uploadResults = await this.s3Service.uploadMultipleThumbnails(
    processingResult.thumbnails.map(t => t.thumbnail_path),
    videoEvent.seller_handle,
    videoEvent.video_id
  );

  // 3. Create thumbnail event
  const thumbnailEvent: ThumbnailGeneratedEvent = {
    video_id: videoEvent.video_id,
    seller_handle: videoEvent.seller_handle,
    thumbnails: uploadResults.map(result => ({
      thumbnail_url: result.url,
      thumbnail_s3_key: result.s3Key,
      frame_timestamp: result.frameTimestamp,
      confidence_score: result.qualityScore,
      is_primary: result.isPrimary
    })),
    primary_thumbnail: uploadResults.find(r => r.isPrimary),
    processing_metadata: {
      video_duration: processingResult.video_duration,
      frames_analyzed: processingResult.frames_analyzed,
      thumbnails_generated: uploadResults.length,
      processing_time_ms: Date.now() - startTime
    }
  };

  // 4. Publish to SNS
  await this.emitThumbnailGeneratedEvent(thumbnailEvent);
}
```

### 4. Product Assembly & Real-time Updates

#### Product Assembly Service
**File**: `apps/product-service/src/events/event-processor.service.ts`

```typescript
async assembleAndSaveProduct(assemblyData: ProductAssemblyData): Promise<void> {
  // 1. Create final product from merged data
  const product: AssembledProduct = {
    seller_handle: assemblyData.seller_handle,
    video_id: assemblyData.video_id,
    title: assemblyData.caption_data.title,
    price: assemblyData.caption_data.price,
    sizes: assemblyData.caption_data.sizes,
    tags: assemblyData.caption_data.tags,
    thumbnails: assemblyData.thumbnail_data.thumbnails,
    primary_thumbnail: assemblyData.thumbnail_data.primary_thumbnail,
    confidence_score: assemblyData.caption_data.confidence_score,
    raw_caption: assemblyData.caption_data.raw_caption,
    processing_metadata: assemblyData.thumbnail_data.processing_metadata,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  // 2. Save to DynamoDB
  await this.dynamoDBService.saveProduct(product);

  // 3. Clean up staging data
  await this.dynamoDBService.deleteProductAssemblyData(
    assemblyData.video_id,
    assemblyData.seller_handle
  );

  // 4. Notify frontend via WebSocket
  await this.notifyNewProduct(product);
}

private async notifyNewProduct(product: AssembledProduct): Promise<void> {
  const newProductEvent: NewProductEvent = {
    event_type: 'new_product',
    product,
    timestamp: new Date().toISOString(),
  };

  // Broadcast via WebSocket
  this.webSocketGateway.broadcastNewProduct(newProductEvent);

  // Optional: Publish to SNS for other services
  if (this.newProductTopicArn) {
    await this.snsClient.send(new PublishCommand({
      TopicArn: this.newProductTopicArn,
      Message: JSON.stringify(newProductEvent),
    }));
  }
}
```

#### WebSocket Broadcasting
**File**: `apps/product-service/src/websocket/websocket.gateway.ts`

```typescript
broadcastNewProduct(event: NewProductEvent): void {
  const sellerHandle = event.product.seller_handle;
  const roomName = `seller:${sellerHandle}`;

  // Broadcast to all clients subscribed to this seller
  this.server.to(roomName).emit('new_product', {
    event_type: 'new_product',
    product: event.product,
    timestamp: event.timestamp,
  });

  const subscribedClients = this.subscriptions.get(sellerHandle) || [];
  this.logger.log(`Broadcasted new product to ${subscribedClients.length} clients`);
}
```

### 5. Frontend Real-time Updates

#### WebSocket Hook
**File**: `apps/frontend/src/hooks/useProductUpdates.ts`

```typescript
export const useProductUpdates = (sellerHandle: string | null) => {
  const { addNewProduct } = useUpdateProductCache();

  useEffect(() => {
    if (!sellerHandle) return;

    const socket = io(`${PRODUCT_SERVICE_URL}/products`);

    socket.on('connect', () => {
      socket.emit('subscribe_to_seller', { seller_handle: sellerHandle });
    });

    socket.on('new_product', (event: NewProductEvent) => {
      if (event.product.seller_handle === sellerHandle) {
        // Update React Query cache
        addNewProduct(sellerHandle, event.product);

        // Show success notification
        toast.success(`New product added: ${event.product.title}`);
      }
    });

    return () => socket.disconnect();
  }, [sellerHandle]);
};
```

#### React Query Cache Update
**File**: `apps/frontend/src/hooks/useProducts.ts`

```typescript
export const useUpdateProductCache = () => {
  const queryClient = useQueryClient();

  const addNewProduct = (sellerHandle: string, newProduct: AssembledProduct) => {
    // Update main products query cache
    queryClient.setQueryData(
      productKeys.shopProducts(sellerHandle),
      (oldData: any) => {
        if (!oldData?.data) return oldData;

        return {
          ...oldData,
          data: {
            ...oldData.data,
            products: [newProduct, ...oldData.data.products], // Add to beginning
            pagination: {
              ...oldData.data.pagination,
              count: oldData.data.pagination.count + 1,
            },
          },
        };
      }
    );

    // Cache individual product
    queryClient.setQueryData(
      productKeys.product(sellerHandle, newProduct.video_id),
      { success: true, data: newProduct }
    );
  };

  return { addNewProduct };
};
```

## 🎯 Key Success Metrics

### Performance Benchmarks
- **Account creation**: < 3 seconds
- **Video processing**: 5-10 minutes end-to-end
- **Real-time updates**: < 1 second from save to display
- **WebSocket connection**: < 2 seconds to establish

### Processing Pipeline Stats
- **Caption parsing**: ~2-3 seconds per video
- **Thumbnail generation**: ~5-8 seconds per video
- **Product assembly**: < 1 second
- **Database operations**: < 500ms per operation

### User Experience Metrics
- **Trial conversion rate**: Target 15-20%
- **Real-time update success**: > 99%
- **Mobile responsiveness**: < 3 second load times
- **WebSocket reliability**: > 99.5% uptime

This completes the comprehensive technical documentation connecting all the dots from account creation to real-time product display! 🎉
