# Product Service Environment Configuration

# Server Configuration
PORT=3002
NODE_ENV=development
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key-id
AWS_SECRET_ACCESS_KEY=your-secret-access-key

# DynamoDB Tables
DYNAMODB_PRODUCTS_TABLE=tiktok-commerce-products-dev
DYNAMODB_STAGING_TABLE=tiktok-commerce-staging-dev

# DynamoDB Performance Settings
DYNAMODB_MAX_RETRIES=3
DYNAMODB_BASE_RETRY_DELAY=100
DYNAMODB_MAX_RETRY_DELAY=5000

# SQS Queues (from AI workers)
SQS_CAPTION_ANALYSIS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/123456789012/tiktok-commerce-dev-caption-analysis
SQS_THUMBNAIL_GENERATION_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/123456789012/tiktok-commerce-dev-thumbnail-generation

# SNS Topics
SNS_NEW_PRODUCT_TOPIC_ARN=arn:aws:sns:us-east-1:123456789012:tiktok-commerce-dev-new-product

# Logging
LOG_LEVEL=info

# Rate Limiting
THROTTLE_TTL=60000
THROTTLE_LIMIT=100

# WebSocket Configuration
WEBSOCKET_CORS_ORIGINS=http://localhost:3000,http://localhost:8080
