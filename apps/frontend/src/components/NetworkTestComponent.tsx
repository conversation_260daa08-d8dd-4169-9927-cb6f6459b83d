import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { NetworkStatusIndicator, ConnectionQualityBadge, useNetworkAwareActions } from './NetworkStatusIndicator';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { authApi } from '@/lib/api';
import { toast } from 'sonner';

// Test component to demonstrate network error handling
export const NetworkTestComponent = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { executeWithNetworkCheck, testConnectivity } = useNetworkAwareActions();
  const networkStatus = useNetworkStatus();

  const testApiCall = async () => {
    setIsLoading(true);
    try {
      await executeWithNetworkCheck(async () => {
        // This will fail if there's no internet connection
        return authApi.validateHandle({ handle: 'test-handle' });
      });
      toast.success('API call successful!');
    } catch (error: any) {
      // Error is already handled by network error handler
      console.error('API call failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const testConnectivityCheck = async () => {
    setIsLoading(true);
    try {
      const isConnected = await testConnectivity();
      if (isConnected) {
        toast.success('Internet connection is working!');
      } else {
        toast.error('No internet connection detected');
      }
    } catch (error) {
      toast.error('Connectivity test failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Network Status Test
          <NetworkStatusIndicator showText size="sm" />
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Network Status */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Current Status:</h4>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">
              {networkStatus.isOnline ? 'Online' : 'Offline'}
            </span>
            <ConnectionQualityBadge />
          </div>
          
          {networkStatus.effectiveType && (
            <div className="text-xs text-muted-foreground">
              Connection: {networkStatus.effectiveType.toUpperCase()}
              {networkStatus.downlink && ` • ${networkStatus.downlink.toFixed(1)}Mbps`}
              {networkStatus.rtt && ` • ${networkStatus.rtt}ms latency`}
            </div>
          )}
        </div>

        {/* Test Buttons */}
        <div className="space-y-2">
          <Button
            onClick={testApiCall}
            disabled={isLoading}
            className="w-full"
            variant="outline"
          >
            {isLoading ? 'Testing...' : 'Test API Call'}
          </Button>
          
          <Button
            onClick={testConnectivityCheck}
            disabled={isLoading}
            className="w-full"
            variant="outline"
          >
            {isLoading ? 'Testing...' : 'Test Connectivity'}
          </Button>
        </div>

        {/* Instructions */}
        <div className="text-xs text-muted-foreground space-y-1">
          <p><strong>To test offline behavior:</strong></p>
          <ol className="list-decimal list-inside space-y-1 ml-2">
            <li>Turn off your WiFi/internet connection</li>
            <li>Click "Test API Call" to see offline error</li>
            <li>Turn your connection back on</li>
            <li>Notice the "connection restored" message</li>
          </ol>
        </div>

        {/* Network Details (for debugging) */}
        {process.env.NODE_ENV === 'development' && (
          <details className="text-xs">
            <summary className="cursor-pointer text-muted-foreground">
              Debug Info
            </summary>
            <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
              {JSON.stringify(networkStatus, null, 2)}
            </pre>
          </details>
        )}
      </CardContent>
    </Card>
  );
};
